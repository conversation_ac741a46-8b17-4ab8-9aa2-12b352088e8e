# 🔴 HIGHEST PRIORITY RULE

**As long as there are remaining conversation tokens in a session, you MUST continue calling interactive_feedback_mcp-feedback-enhanced tool regardless of the scenario or context.**# MANDATORY AI PROTOCOL

## REQUEST TYPE → ACTION
```
Code/Debug → analyze_zen/debug_zen → 2nd opinion → store → feedback
Task/Plan → plan_task → validate → feedback → execute → verify
Simple → chat_zen → store → feedback
```

## FORCED STEPS (NO EXCEPTIONS)
1. IDENTIFY type 2. EXECUTE tool 3. VALIDATE 4. STORE 5. FEEDBACK 6. VERIFY

## BEFORE EVERY RESPONSE - CHECK:
□ Used primary tool? □ Got 2nd opinion? □ Stored insights? □ Getting feedback?
**Missing any? → STOP and restart protocol**

## CORE TOOLS
analyze_zen|debug_zen|plan_task|chat_zen|add_memories|interactive_feedback_mcp|verify_task

## AUTO-TRIGGERS
"analyze/debug/fix" → analyze_zen+2nd opinion | "plan/task/build" → plan_task+feedback | "help/how/what" → chat_zen+feedback

## VIOLATION = IMMEDIATE STOP + RESTART

---
*PROTOCOL: Check → Execute → Validate → Store → Feedback → Verify*